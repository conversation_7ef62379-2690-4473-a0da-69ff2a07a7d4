diff --git a/product-detail-common-module-api/src/main/java/com/sankuai/dzshoppingguide/product/detail/spi/inventory/vo/InventoryInfoDTO.java b/product-detail-common-module-api/src/main/java/com/sankuai/dzshoppingguide/product/detail/spi/inventory/vo/InventoryInfoDTO.java
new file mode 100644
index 00000000..da59d5f2
--- /dev/null
+++ b/product-detail-common-module-api/src/main/java/com/sankuai/dzshoppingguide/product/detail/spi/inventory/vo/InventoryInfoDTO.java
@@ -0,0 +1,28 @@
+package com.sankuai.dzshoppingguide.product.detail.spi.inventory.vo;
+
+import lombok.Data;
+
+import java.io.Serializable;
+
+@Data
+public class InventoryInfoDTO implements Serializable {
+    /**
+     * 属性key
+     */
+    private String key;
+
+    /**
+     * 属性value
+     */
+    private String value;
+
+    /**
+     * 属性中文名称
+     */
+    private String name;
+
+    /**
+     * 属性展示优先级,从1开始,值越大,优先级越低
+     */
+    private int priority;
+}
