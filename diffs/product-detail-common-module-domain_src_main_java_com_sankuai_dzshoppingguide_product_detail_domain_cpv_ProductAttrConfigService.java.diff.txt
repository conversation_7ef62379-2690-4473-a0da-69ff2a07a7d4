diff --git a/product-detail-common-module-domain/src/main/java/com/sankuai/dzshoppingguide/product/detail/domain/cpv/ProductAttrConfigService.java b/product-detail-common-module-domain/src/main/java/com/sankuai/dzshoppingguide/product/detail/domain/cpv/ProductAttrConfigService.java
index 0bc7ce71..35bbd9a8 100644
--- a/product-detail-common-module-domain/src/main/java/com/sankuai/dzshoppingguide/product/detail/domain/cpv/ProductAttrConfigService.java
+++ b/product-detail-common-module-domain/src/main/java/com/sankuai/dzshoppingguide/product/detail/domain/cpv/ProductAttrConfigService.java
@@ -4,11 +4,14 @@ import com.alibaba.fastjson.JSONObject;
 import com.alibaba.fastjson.TypeReference;
 import com.dianping.lion.Environment;
 import com.dianping.lion.client.Lion;
-import com.sankuai.dzshoppingguide.product.detail.domain.exception.ProductDetailFatalError;
 import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
+import com.sankuai.dzshoppingguide.product.detail.domain.cpv.flash.CpvAttrConfigService;
+import com.sankuai.dzshoppingguide.product.detail.domain.exception.ProductDetailFatalError;
 import org.springframework.beans.factory.InitializingBean;
+import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.stereotype.Service;
 
+import java.util.Collections;
 import java.util.HashSet;
 import java.util.Map;
 import java.util.Set;
@@ -20,6 +23,8 @@ import java.util.concurrent.ConcurrentHashMap;
  */
 @Service
 public class ProductAttrConfigService implements InitializingBean {
+    @Autowired
+    private CpvAttrConfigService cpvExtraAttrConfigService;
 
     private final String PRODUCT_ATTR_CONFIG = "com.sankuai.dzshoppingguide.detail.commonmodule.product.attr.config";
 
@@ -39,7 +44,17 @@ public class ProductAttrConfigService implements InitializingBean {
     }
 
     public Set<String> getProductAttrConfig(ProductTypeEnum productType, int secondProductCategory) {
-        return attrConfigMap.getOrDefault(buildLionKey(productType, secondProductCategory), new HashSet<>());
+        if (productType == null || secondProductCategory <= 0) {
+            return Collections.emptySet();
+        }
+        
+        Set<String> productExtraAttrs = cpvExtraAttrConfigService.getProductExtraAttrs(secondProductCategory);
+        Set<String> productAttrConfig = attrConfigMap.getOrDefault(buildLionKey(productType, secondProductCategory), new HashSet<>());
+        
+        // 使用不可变集合避免外部修改
+        Set<String> result = new HashSet<>(productAttrConfig);
+        result.addAll(productExtraAttrs);
+        return Collections.unmodifiableSet(result);
     }
 
     @Override
