diff --git a/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/fetcher/brand/BrandInfo.java b/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/fetcher/brand/BrandInfo.java
new file mode 100644
index 00000000..ee9be94d
--- /dev/null
+++ b/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/fetcher/brand/BrandInfo.java
@@ -0,0 +1,14 @@
+package com.sankuai.dzshoppingguide.product.detail.application.fetcher.brand;
+
+import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
+
+import lombok.AllArgsConstructor;
+import lombok.Data;
+import lombok.NoArgsConstructor;
+
+@Data
+@AllArgsConstructor
+@NoArgsConstructor
+public class BrandInfo extends FetcherReturnValueDTO {
+    private String brandName;
+}
