diff --git a/product-detail-common-module-application/src/test/java/com/sankuai/dzshoppingguide/product/detail/application/builder/inventory/InventoryDetailBuilderTest.java b/product-detail-common-module-application/src/test/java/com/sankuai/dzshoppingguide/product/detail/application/builder/inventory/InventoryDetailBuilderTest.java
new file mode 100644
index 00000000..ec668f35
--- /dev/null
+++ b/product-detail-common-module-application/src/test/java/com/sankuai/dzshoppingguide/product/detail/application/builder/inventory/InventoryDetailBuilderTest.java
@@ -0,0 +1,183 @@
+package com.sankuai.dzshoppingguide.product.detail.application.builder.inventory;
+
+import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.AbstractBuilder;
+import com.sankuai.dzshoppingguide.product.detail.application.fetcher.brand.BrandInfo;
+import com.sankuai.dzshoppingguide.product.detail.application.fetcher.brand.BrandInfoFetcher;
+import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
+import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
+import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryName;
+import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoFetcher;
+import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoResult;
+import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
+import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
+import com.sankuai.dzshoppingguide.product.detail.domain.cpv.flash.CpvAttrConfigService;
+import com.sankuai.dzshoppingguide.product.detail.spi.inventory.vo.InventoryDetail;
+import com.sankuai.dzshoppingguide.product.detail.spi.inventory.vo.InventoryInfoDTO;
+import com.sankuai.dzshoppingguide.product.detail.spi.inventory.vo.InventoryModuleVO;
+import com.sankuai.general.product.query.center.client.dto.AttrDTO;
+import org.junit.Assert;
+import org.junit.Before;
+import org.junit.Test;
+import org.junit.runner.RunWith;
+import org.mockito.InjectMocks;
+import org.mockito.Mock;
+import org.mockito.MockitoAnnotations;
+import org.powermock.api.mockito.PowerMockito;
+import org.powermock.core.classloader.annotations.PrepareForTest;
+import org.powermock.modules.junit4.PowerMockRunner;
+
+import java.util.*;
+
+import static org.mockito.ArgumentMatchers.eq;
+import static org.mockito.Mockito.when;
+
+@RunWith(PowerMockRunner.class)
+@PrepareForTest({AbstractBuilder.class})
+public class InventoryDetailBuilderTest {
+
+    @Mock
+    private CpvAttrConfigService cpvExtraAttrConfigService;
+
+    @InjectMocks
+    private InventoryDetailBuilder inventoryDetailBuilder;
+
+    @Before
+    public void setUp() {
+        MockitoAnnotations.initMocks(this);
+    }
+
+    @Test
+    public void testDoBuild_WhenProductAttrAndCategoryIdNotPresent_ReturnNull() throws Exception {
+        // Given
+        InventoryDetailBuilder builder = PowerMockito.spy(inventoryDetailBuilder);
+        PowerMockito.doReturn(Optional.empty()).when(builder, "getDependencyResult", eq(ProductAttrFetcher.class), eq(ProductAttr.class));
+        PowerMockito.doReturn(Optional.empty()).when(builder, "getDependencyResult", eq(ProductCategoryFetcher.class), eq(ProductCategory.class));
+        PowerMockito.doReturn(Optional.empty()).when(builder, "getDependencyResult", eq(BrandInfoFetcher.class), eq(BrandInfo.class));
+        PowerMockito.doReturn(Optional.empty()).when(builder, "getDependencyResult", eq(AdditionInfoFetcher.class), eq(AdditionInfoResult.class));
+        // When
+        InventoryModuleVO result = builder.doBuild();
+
+        // Then
+        Assert.assertNull(result);
+    }
+
+    @Test
+    public void testDoBuild_WhenProductExtraAttrsEmpty_ReturnNull() throws Exception {
+        // Given
+        InventoryDetailBuilder builder = PowerMockito.spy(inventoryDetailBuilder);
+
+        ProductAttr productAttr = new ProductAttr(new HashMap<>());
+        ProductCategory productCategory = new ProductCategory(1, 2, 3);
+        BrandInfo brandInfo = new BrandInfo();
+        brandInfo.setBrandName("brandName");
+
+        AdditionInfoResult additionInfoResult = new AdditionInfoResult();
+        additionInfoResult.setAdditionInfo("additionInfo");
+
+        PowerMockito.doReturn(Optional.of(productAttr)).when(builder, "getDependencyResult", eq(ProductAttrFetcher.class), eq(ProductAttr.class));
+        PowerMockito.doReturn(Optional.of(productCategory)).when(builder, "getDependencyResult", eq(ProductCategoryFetcher.class), eq(ProductCategory.class));
+        PowerMockito.doReturn(Optional.of(brandInfo)).when(builder, "getDependencyResult", eq(BrandInfoFetcher.class), eq(BrandInfo.class));
+        PowerMockito.doReturn(Optional.of(additionInfoResult)).when(builder, "getDependencyResult", eq(AdditionInfoFetcher.class), eq(AdditionInfoResult.class));
+
+        when(cpvExtraAttrConfigService.getProductExtraAttrDTOs(2)).thenReturn(Collections.emptySet());
+
+        // When
+        InventoryModuleVO result = builder.doBuild();
+
+        // Then
+        Assert.assertNull(result);
+    }
+
+    @Test
+    public void testDoBuild_WhenInventoryDetailsEmpty_ReturnNull() throws Exception {
+        // Given
+        InventoryDetailBuilder builder = PowerMockito.spy(inventoryDetailBuilder);
+
+        Map<String, AttrDTO> attrMap = new HashMap<>();
+        AttrDTO attrDTO = new AttrDTO();
+        attrDTO.setValue(Collections.emptyList());
+        attrMap.put("testKey", attrDTO);
+        ProductAttr productAttr = new ProductAttr(attrMap);
+
+        ProductCategory productCategory = new ProductCategory(1, 2, 3);
+
+        Set<InventoryInfoDTO> productExtraAttrs = new HashSet<>();
+        InventoryInfoDTO infoDTO = new InventoryInfoDTO();
+        infoDTO.setKey("testKey");
+        infoDTO.setName("testName");
+        productExtraAttrs.add(infoDTO);
+
+        PowerMockito.doReturn(Optional.of(productAttr)).when(builder, "getDependencyResult", eq(ProductAttrFetcher.class), eq(ProductAttr.class));
+        PowerMockito.doReturn(Optional.of(productCategory)).when(builder, "getDependencyResult", eq(ProductCategoryFetcher.class), eq(ProductCategory.class));
+
+        BrandInfo brandInfo = new BrandInfo();
+        brandInfo.setBrandName("brandName");
+        AdditionInfoResult additionInfoResult = new AdditionInfoResult();
+        additionInfoResult.setAdditionInfo("additionInfo");
+        PowerMockito.doReturn(Optional.of(brandInfo)).when(builder, "getDependencyResult", eq(BrandInfoFetcher.class), eq(BrandInfo.class));
+        PowerMockito.doReturn(Optional.of(additionInfoResult)).when(builder, "getDependencyResult", eq(AdditionInfoFetcher.class), eq(AdditionInfoResult.class));
+
+        when(cpvExtraAttrConfigService.getProductExtraAttrDTOs(2)).thenReturn(productExtraAttrs);
+
+        // When
+        InventoryModuleVO result = builder.doBuild();
+
+        // Then
+        Assert.assertNull(result);
+    }
+
+    @Test
+    public void testDoBuild_Success() throws Exception {
+        // Given
+        InventoryDetailBuilder builder = PowerMockito.spy(inventoryDetailBuilder);
+
+        // Setup ProductAttr
+        Map<String, AttrDTO> attrMap = new HashMap<>();
+        AttrDTO attrDTO = new AttrDTO();
+        attrDTO.setValue(Collections.singletonList("attrValue"));
+        attrMap.put("testKey", attrDTO);
+        ProductAttr productAttr = new ProductAttr(attrMap);
+
+        // Setup ProductCategory
+        ProductCategory productCategory = new ProductCategory(1, 2, 3);
+
+        // Setup ProductCategoryName
+        ProductCategoryName categoryName = ProductCategoryName.builder()
+                .secondCategoryName("second")
+                .thirdCategoryName("third")
+                .fourthCategoryName("fourth")
+                .build();
+
+        // Setup InventoryInfoDTO
+        Set<InventoryInfoDTO> productExtraAttrs = new HashSet<>();
+        InventoryInfoDTO infoDTO = new InventoryInfoDTO();
+        infoDTO.setKey("testKey");
+        infoDTO.setName("testName");
+        productExtraAttrs.add(infoDTO);
+
+        PowerMockito.doReturn(Optional.of(productAttr)).when(builder, "getDependencyResult", eq(ProductAttrFetcher.class), eq(ProductAttr.class));
+        PowerMockito.doReturn(Optional.of(productCategory)).when(builder, "getDependencyResult", eq(ProductCategoryFetcher.class), eq(ProductCategory.class));
+
+        BrandInfo brandInfo = new BrandInfo();
+        brandInfo.setBrandName("brandName");
+        AdditionInfoResult additionInfoResult = new AdditionInfoResult();
+        additionInfoResult.setAdditionInfo("additionInfo");
+        PowerMockito.doReturn(Optional.of(brandInfo)).when(builder, "getDependencyResult", eq(BrandInfoFetcher.class), eq(BrandInfo.class));
+        PowerMockito.doReturn(Optional.of(additionInfoResult)).when(builder, "getDependencyResult", eq(AdditionInfoFetcher.class), eq(AdditionInfoResult.class));
+
+        when(cpvExtraAttrConfigService.getProductExtraAttrDTOs(2)).thenReturn(productExtraAttrs);
+
+        // When
+        InventoryModuleVO result = builder.doBuild();
+
+        // Then
+        Assert.assertNotNull(result);
+        Assert.assertNotNull(result.getInventoryDetails());
+        Assert.assertEquals(1, result.getInventoryDetails().size());
+
+        // Verify category detail
+        InventoryDetail categoryDetail = result.getInventoryDetails().get(0);
+        Assert.assertEquals("testName", categoryDetail.getTitle());
+        Assert.assertEquals("attrValue", categoryDetail.getSubTitle());
+    }
+}
\ No newline at end of file
