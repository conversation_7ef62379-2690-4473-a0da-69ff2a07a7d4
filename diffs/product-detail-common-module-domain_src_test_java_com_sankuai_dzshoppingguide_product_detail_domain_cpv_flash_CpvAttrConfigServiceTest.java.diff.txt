diff --git a/product-detail-common-module-domain/src/test/java/com/sankuai/dzshoppingguide/product/detail/domain/cpv/flash/CpvAttrConfigServiceTest.java b/product-detail-common-module-domain/src/test/java/com/sankuai/dzshoppingguide/product/detail/domain/cpv/flash/CpvAttrConfigServiceTest.java
new file mode 100644
index 00000000..8cff6bfa
--- /dev/null
+++ b/product-detail-common-module-domain/src/test/java/com/sankuai/dzshoppingguide/product/detail/domain/cpv/flash/CpvAttrConfigServiceTest.java
@@ -0,0 +1,142 @@
+package com.sankuai.dzshoppingguide.product.detail.domain.cpv.flash;
+
+import com.dianping.lion.Environment;
+import com.dianping.lion.client.Lion;
+import com.sankuai.dzshoppingguide.product.detail.spi.inventory.vo.InventoryInfoDTO;
+import org.junit.Before;
+import org.junit.Test;
+import org.junit.runner.RunWith;
+import org.mockito.InjectMocks;
+import org.mockito.MockitoAnnotations;
+import org.powermock.api.mockito.PowerMockito;
+import org.powermock.core.classloader.annotations.PowerMockIgnore;
+import org.powermock.core.classloader.annotations.PrepareForTest;
+import org.powermock.modules.junit4.PowerMockRunner;
+
+import java.util.Map;
+import java.util.Set;
+
+import static org.junit.Assert.assertEquals;
+import static org.junit.Assert.assertTrue;
+import static org.mockito.ArgumentMatchers.*;
+import static org.mockito.Mockito.when;
+
+@RunWith(PowerMockRunner.class)
+@PrepareForTest({Lion.class, Environment.class})
+@PowerMockIgnore({"javax.management.*", "javax.xml.*", "ch.qos.logback.*", "org.slf4j.*"})
+public class CpvAttrConfigServiceTest {
+
+    @InjectMocks
+    private CpvAttrConfigService cpvAttrConfigService;
+
+    private static final String TEST_CONFIG = "{\"123\":[{\"key\":\"attr1\",\"name\":\"name1\",\"priority\":1},{\"key\":\"attr2\",\"name\":\"name2\",\"priority\":2}]}";
+    private static final String EMPTY_CONFIG = "{}";
+    private static final String INVALID_CONFIG = "invalid json";
+
+    @Before
+    public void setUp() {
+        MockitoAnnotations.initMocks(this);
+        // PowerMockito.mockStatic(Lion.class);
+        // when(Lion.getString(anyString(), anyString())).thenReturn(TEST_CONFIG);
+    }
+
+    @Test
+    public void testGetProductExtraAttrs() throws Exception {
+        PowerMockito.mockStatic(Lion.class);
+        when(Lion.getString(any(), any())).thenReturn(TEST_CONFIG);
+        PowerMockito.doNothing().when(Lion.class,"addConfigListener", anyString(), any());
+
+        // Initialize service
+        cpvAttrConfigService.afterPropertiesSet();
+
+        // Test with existing category
+        Set<String> attrs = cpvAttrConfigService.getProductExtraAttrs(123);
+        assertEquals(2, attrs.size());
+        assertTrue(attrs.contains("attr1"));
+        assertTrue(attrs.contains("attr2"));
+
+        // Test with non-existing category
+        Set<String> emptyAttrs = cpvAttrConfigService.getProductExtraAttrs(999);
+        assertTrue(emptyAttrs.isEmpty());
+    }
+
+    @Test
+    public void testGetProductExtraAttrDTOs() throws Exception {
+        PowerMockito.mockStatic(Lion.class);
+        when(Lion.getString(any(), any())).thenReturn(TEST_CONFIG);
+        PowerMockito.doNothing().when(Lion.class,"addConfigListener", anyString(), any());
+
+        // Initialize service
+        cpvAttrConfigService.afterPropertiesSet();
+
+        // Test with existing category
+        Set<InventoryInfoDTO> dtos = cpvAttrConfigService.getProductExtraAttrDTOs(123);
+        assertEquals(2, dtos.size());
+
+        // Test with non-existing category
+        Set<InventoryInfoDTO> emptyDtos = cpvAttrConfigService.getProductExtraAttrDTOs(999);
+        assertTrue(emptyDtos.isEmpty());
+    }
+
+    @Test
+    public void testGetCategoryPriority() throws Exception {
+        PowerMockito.mockStatic(Lion.class);
+        when(Lion.getString(any(), any())).thenReturn(TEST_CONFIG);
+        PowerMockito.doNothing().when(Lion.class,"addConfigListener", anyString(), any());
+        // Initialize service
+        cpvAttrConfigService.afterPropertiesSet();
+
+        // Test with existing category
+        Map<String, Integer> priorities = cpvAttrConfigService.getCategoryPriority(123);
+        assertEquals(2, priorities.size());
+        assertEquals(Integer.valueOf(1), priorities.get("name1"));
+        assertEquals(Integer.valueOf(2), priorities.get("name2"));
+
+        // Test with non-existing category
+        Map<String, Integer> emptyPriorities = cpvAttrConfigService.getCategoryPriority(999);
+        assertTrue(emptyPriorities.isEmpty());
+    }
+
+    // @Test(expected = ProductDetailFatalError.class)
+    // public void testParseWithNullConfig() throws Exception {
+    //     when(Lion.getString(eq("test-app"), any())).thenReturn(null);
+    //     cpvAttrConfigService.afterPropertiesSet();
+    // }
+    //
+    // @Test(expected = ProductDetailFatalError.class)
+    // public void testParseWithInvalidConfig() throws Exception {
+    //     when(Lion.getString(eq("test-app"), any())).thenReturn(INVALID_CONFIG);
+    //     cpvAttrConfigService.afterPropertiesSet();
+    // }
+
+    @Test
+    public void testParseWithEmptyConfig() throws Exception {
+        PowerMockito.mockStatic(Lion.class);
+        when(Lion.getString(any(), any())).thenReturn(EMPTY_CONFIG);
+        PowerMockito.doNothing().when(Lion.class,"addConfigListener", anyString(), any());
+        // when(Lion.getString(eq("test-app"), any())).thenReturn(EMPTY_CONFIG);
+        cpvAttrConfigService.afterPropertiesSet();
+
+        // Verify empty results for any category
+        assertTrue(cpvAttrConfigService.getProductExtraAttrs(123).isEmpty());
+        assertTrue(cpvAttrConfigService.getProductExtraAttrDTOs(123).isEmpty());
+        assertTrue(cpvAttrConfigService.getCategoryPriority(123).isEmpty());
+    }
+
+    // @Test
+    // public void testConfigListener() throws Exception {
+    //     // Initialize service
+    //     cpvAttrConfigService.afterPropertiesSet();
+    //
+    //     // Verify initial state
+    //     Set<String> initialAttrs = cpvAttrConfigService.getProductExtraAttrs(123);
+    //     assertEquals(2, initialAttrs.size());
+    //
+    //     // Simulate config update
+    //     String newConfig = "{\"123\":[{\"key\":\"attr3\",\"name\":\"name3\",\"priority\":3}]}";
+    //     when(Lion.getString(eq("test-app"), any())).thenReturn(newConfig);
+    //
+    //     // Trigger config listener
+    //     // verify(lion, times(1)).addConfigListener(any(), any());
+    // }
+}
\ No newline at end of file
