diff --git a/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/service/CompositeAtomService.java b/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/service/CompositeAtomService.java
index b109b3bd..e0e36caa 100644
--- a/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/service/CompositeAtomService.java
+++ b/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/service/CompositeAtomService.java
@@ -16,6 +16,8 @@ import com.sankuai.dztheme.massagebook.theme.res.ReserveQueryResponse;
 import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
 import com.sankuai.mdp.dzrank.scenes.api.request.RankLabelDataRequest;
 import com.sankuai.mdp.dzrank.scenes.api.response.dto.RankingResult;
+import com.sankuai.meituan.shangou.standardquery.thrift.command.ListSgBrandByIdsCommand;
+import com.sankuai.meituan.shangou.standardquery.thrift.domain.BrandVo;
 import com.sankuai.mpproduct.idservice.api.request.BizProductIdConvertRequest;
 import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
 import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
@@ -33,6 +35,13 @@ import java.util.concurrent.CompletableFuture;
  * @desc
  */
 public interface CompositeAtomService {
+    /**
+     * 查询品牌信息
+     * @param command
+     * @return
+     */
+    CompletableFuture<BrandVo> queryBrandInfoByBrandId(ListSgBrandByIdsCommand command);
+
     /**
      * 查询折扣卡信息
      * @param request
