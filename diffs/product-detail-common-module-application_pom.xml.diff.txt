diff --git a/product-detail-common-module-application/pom.xml b/product-detail-common-module-application/pom.xml
index 02983577..f24a9754 100644
--- a/product-detail-common-module-application/pom.xml
+++ b/product-detail-common-module-application/pom.xml
@@ -252,20 +252,24 @@
             <artifactId>general-unified-search-api</artifactId>
             <version>1.8.15</version>
         </dependency>
-        <!--bestShop-->
-<!--        &lt;!&ndash;到综团购团单详情结构化信息查询服务&ndash;&gt;-->
-<!--        <dependency>-->
-<!--            <groupId>com.dianping.deal</groupId>-->
-<!--            <artifactId>deal-struct-query-api</artifactId>-->
-<!--            <version>1.0.5</version>-->
-<!--        </dependency>-->
-<!--        &lt;!&ndash;到综团购团单详情结构化信息查询服务&ndash;&gt;-->
 
+        <dependency>
+            <groupId>org.mockito</groupId>
+            <artifactId>mockito-core</artifactId>
+            <version>2.28.2</version>
+            <scope>test</scope>
+        </dependency>
         <dependency>
             <groupId>org.powermock</groupId>
             <artifactId>powermock-module-junit4</artifactId>
-            <!--<version>2.0.0</version>-->
-            <version>2.0.7</version>
+            <version>2.0.9</version>
+            <scope>test</scope>
+        </dependency>
+        <dependency>
+            <groupId>org.powermock</groupId>
+            <artifactId>powermock-api-mockito2</artifactId>
+            <version>2.0.9</version>
+            <scope>test</scope>
         </dependency>
 
         <dependency>
@@ -285,12 +289,6 @@
             <artifactId>common-base</artifactId>
             <version>2.13.70</version>
         </dependency>
-
-        <dependency>
-            <groupId>org.powermock</groupId>
-            <artifactId>powermock-api-mockito2</artifactId>
-            <version>2.0.0</version>
-        </dependency>
         <!--标签查询接口-->
         <dependency>
             <groupId>com.dianping.deal</groupId>
@@ -444,6 +442,15 @@
             <artifactId>arts-client</artifactId>
             <version>ES_0.4.22</version>
         </dependency>
+        <!-- cpv进场需求 -->
+
+        <dependency>
+            <groupId>com.sankuai.meituan.shangou.standard</groupId>
+            <artifactId>shangou_product_standardquery_client</artifactId>
+            <version>1.4.4</version>
+        </dependency>
+        <!-- cpv进场需求 -->
+        
     </dependencies>
 
 
