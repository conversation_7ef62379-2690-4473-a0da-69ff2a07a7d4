diff --git a/product-detail-common-module-api/src/main/java/com/sankuai/dzshoppingguide/product/detail/spi/inventory/vo/InventoryDetail.java b/product-detail-common-module-api/src/main/java/com/sankuai/dzshoppingguide/product/detail/spi/inventory/vo/InventoryDetail.java
new file mode 100644
index 00000000..39a843a9
--- /dev/null
+++ b/product-detail-common-module-api/src/main/java/com/sankuai/dzshoppingguide/product/detail/spi/inventory/vo/InventoryDetail.java
@@ -0,0 +1,36 @@
+package com.sankuai.dzshoppingguide.product.detail.spi.inventory.vo;
+
+import com.dianping.mobile.framework.annotation.MobileDo;
+
+import java.io.Serializable;
+
+@MobileDo(id = 0xefc4)
+public class InventoryDetail implements Serializable {
+    /**
+    * 副标题
+    */
+    @MobileDo.MobileField(key = 0xd894)
+    private String subTitle;
+
+    /**
+    * 主标题
+    */
+    @MobileDo.MobileField(key = 0x24cc)
+    private String title;
+
+    public String getSubTitle() {
+        return subTitle;
+    }
+
+    public void setSubTitle(String subTitle) {
+        this.subTitle = subTitle;
+    }
+
+    public String getTitle() {
+        return title;
+    }
+
+    public void setTitle(String title) {
+        this.title = title;
+    }
+}
\ No newline at end of file
