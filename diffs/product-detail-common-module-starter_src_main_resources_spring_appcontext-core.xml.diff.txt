diff --git a/product-detail-common-module-starter/src/main/resources/spring/appcontext-core.xml b/product-detail-common-module-starter/src/main/resources/spring/appcontext-core.xml
index 2fb58dd7..4523c080 100644
--- a/product-detail-common-module-starter/src/main/resources/spring/appcontext-core.xml
+++ b/product-detail-common-module-starter/src/main/resources/spring/appcontext-core.xml
@@ -12,47 +12,4 @@
     http://www.sankuai.com/schema/athena-client http://www.sankuai.com/schema/athena-client-1.0.xsd">
 
     <athena-client:annotation-driven/>
-
-<!--    <bean id="userCollectionPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig"-->
-<!--          p:maxActive="100"-->
-<!--          p:maxIdle="150"-->
-<!--          p:minIdle="10"-->
-<!--          p:testOnBorrow="true"-->
-<!--          p:testOnReturn="true"-->
-<!--          p:testWhileIdle="true"-->
-<!--    />-->
-
-<!--    <bean id="userCollectionProxy" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"-->
-<!--          p:mtThriftPoolConfig-ref="userCollectionPoolConfig"-->
-<!--          p:remoteAppkey="com.sankuai.wpt.user.collection"-->
-<!--          p:appKey="com.sankuai.dztheme.generalproduct"-->
-<!--          p:clusterManager="octo"-->
-<!--          p:serviceInterface="com.sankuai.coll.idl.CollectionService"-->
-<!--          p:timeout="1000"-->
-<!--          p:serverDynamicWeight="true"-->
-<!--          p:remoteServerPort="10666"-->
-<!--    />-->
-
-<!--    <bean id="userCollectionClient" class="com.sankuai.user.collection.client.UserCollectionClient"-->
-<!--          p:iface-ref="userCollectionProxy"-->
-<!--    />-->
-
-    <!--city转换开放平台-->
-    <bean id="mapOpenApiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
-        <property name="serviceInterface" value="com.sankuai.map.open.platform.api.MapOpenApiService"/> <!-- 接口名 -->
-        <property name="remoteAppkey" value="com.sankuai.apigw.map.facadecenter"/>  <!-- 开平服务 Appkey  -->
-        <property name="nettyIO" value="true"/>
-        <property name="async" value="true"/>
-        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
-    </bean>
-
-    <bean id="poiRelationServiceFuture" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
-        <property name="serviceInterface" value="com.dianping.poi.relation.service.api.PoiRelationService" />
-        <property name="appKey" value="com.sankuai.dzshoppingguide.detail.commonmodule" />
-        <property name="remoteServerPort" value="5806" />
-        <property name="remoteAppkey" value="poi-relation-service-web" />
-        <property name="nettyIO" value="true"/>
-        <property name="async" value="true"/>
-        <property name="timeout" value="1000" />
-    </bean>
 </beans>
