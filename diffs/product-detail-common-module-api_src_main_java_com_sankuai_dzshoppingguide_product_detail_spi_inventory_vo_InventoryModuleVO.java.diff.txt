diff --git a/product-detail-common-module-api/src/main/java/com/sankuai/dzshoppingguide/product/detail/spi/inventory/vo/InventoryModuleVO.java b/product-detail-common-module-api/src/main/java/com/sankuai/dzshoppingguide/product/detail/spi/inventory/vo/InventoryModuleVO.java
new file mode 100644
index 00000000..b05f640f
--- /dev/null
+++ b/product-detail-common-module-api/src/main/java/com/sankuai/dzshoppingguide/product/detail/spi/inventory/vo/InventoryModuleVO.java
@@ -0,0 +1,30 @@
+package com.sankuai.dzshoppingguide.product.detail.spi.inventory.vo;
+
+import com.dianping.mobile.framework.annotation.MobileDo;
+import com.meituan.servicecatalog.api.annotations.TypeDoc;
+import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
+import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
+import lombok.Data;
+import lombok.EqualsAndHashCode;
+
+import java.util.List;
+
+/**
+ * https://mobile.sankuai.com/studio/model/info/42206
+ */
+@EqualsAndHashCode(callSuper = true)
+@Data
+@TypeDoc(description = "商品分类和属性信息模型")
+@MobileDo(id = 0x85ac )
+public class InventoryModuleVO extends AbstractModuleVO {
+    @Override
+    public String getModuleKey() {
+        return ModuleKeyConstants.INVENTORY_INFO;
+    }
+
+    @MobileDo.MobileField(key = 0x9989)
+    private List<InventoryDetail> inventoryDetails;
+
+    @MobileDo.MobileField(key = 0xf7ac)
+    private String richText;
+}
