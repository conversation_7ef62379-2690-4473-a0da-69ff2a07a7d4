diff --git a/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/service/impl/CompositeAtomServiceImpl.java b/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/service/impl/CompositeAtomServiceImpl.java
index a43591af..f972030d 100644
--- a/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/service/impl/CompositeAtomServiceImpl.java
+++ b/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/service/impl/CompositeAtomServiceImpl.java
@@ -20,7 +20,7 @@ import com.google.common.collect.Maps;
 import com.meituan.inf.xmdlog.XMDLogFormat;
 import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
 import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
-import com.sankuai.athena.inf.AthenaInf;
+import com.meituan.service.mobile.mtthrift.callback.OctoThriftCallback;
 import com.sankuai.athena.inf.rpc.CallType;
 import com.sankuai.athena.inf.rpc.RpcClient;
 import com.sankuai.dzcard.navigation.api.DzCardExposureService;
@@ -48,6 +48,10 @@ import com.sankuai.mdp.dzrank.scenes.api.RankLabelQuery;
 import com.sankuai.mdp.dzrank.scenes.api.request.RankLabelDataRequest;
 import com.sankuai.mdp.dzrank.scenes.api.response.dto.RankingResult;
 import com.sankuai.mdp.dzshoplist.rank.api.response.Response;
+import com.sankuai.meituan.shangou.standardquery.thrift.command.ListSgBrandByIdsCommand;
+import com.sankuai.meituan.shangou.standardquery.thrift.domain.BrandVo;
+import com.sankuai.meituan.shangou.standardquery.thrift.result.TBListBrandResult;
+import com.sankuai.meituan.shangou.standardquery.thrift.service.BrandThriftService;
 import com.sankuai.mpproduct.idservice.api.request.BizProductIdConvertRequest;
 import com.sankuai.mpproduct.idservice.api.response.BizProductIdConvertResponse;
 import com.sankuai.mpproduct.idservice.api.service.IdService;
@@ -138,6 +142,30 @@ public class CompositeAtomServiceImpl implements CompositeAtomService {
     @MdpThriftClient(remoteAppKey = "com.sankuai.mpproduct.idservice", timeout = 500, testTimeout = 5000,async = true)
     private IdService idService;
 
+    @MdpThriftClient(remoteAppKey = "com.sankuai.nautilus.product.standardquery", timeout = 500, testTimeout = 5000,async = true)
+    private BrandThriftService.AsyncIface brandThriftService;
+
+    @Override
+    public CompletableFuture<BrandVo> queryBrandInfoByBrandId(ListSgBrandByIdsCommand command) {
+        try {
+            OctoThriftCallback<BrandThriftService.AsyncClient.listSgBrandByIds_call, TBListBrandResult> thriftCallback = new OctoThriftCallback<>();
+            brandThriftService.listSgBrandByIds(command,thriftCallback);
+            CompletableFuture<TBListBrandResult> future = ThriftAsyncUtils.getThriftFuture(thriftCallback);
+            return future.thenApply(
+                    result -> Optional.ofNullable(result)
+                            .map(TBListBrandResult::getBrandList)
+                            .orElse(Collections.emptyList())
+                            .stream()
+                            .filter(Objects::nonNull)
+                            .findFirst()
+                            .orElse(null)
+            );
+        } catch (Exception e) {
+            log.error("queryBrandInfoByBrandId error, command={}", JsonCodec.encodeWithUTF8(command), e);
+            return CompletableFuture.completedFuture(null);
+        }
+    }
+
     @Override
     public CompletableFuture<QueryCardInfoDTO> queryDiscountCardInfo(QueryDiscountCardReq request) {
         try {
