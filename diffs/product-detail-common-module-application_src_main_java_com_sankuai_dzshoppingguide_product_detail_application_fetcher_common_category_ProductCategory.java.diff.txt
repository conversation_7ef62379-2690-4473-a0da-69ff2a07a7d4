diff --git a/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/fetcher/common/category/ProductCategory.java b/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/fetcher/common/category/ProductCategory.java
index 6bbb21ca..fbf29396 100644
--- a/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/fetcher/common/category/ProductCategory.java
+++ b/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/fetcher/common/category/ProductCategory.java
@@ -1,6 +1,7 @@
 package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category;
 
 import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
+
 import lombok.Getter;
 
 /**
@@ -14,6 +15,9 @@ public class ProductCategory extends FetcherReturnValueDTO {
 
     private final int productSecondCategoryId;
 
+    /**
+     * 不一定是三级分类id,准确来说应该是末级分类id
+     */
     private final int productThirdCategoryId;
 
     public ProductCategory(final int productFirstCategoryId,
