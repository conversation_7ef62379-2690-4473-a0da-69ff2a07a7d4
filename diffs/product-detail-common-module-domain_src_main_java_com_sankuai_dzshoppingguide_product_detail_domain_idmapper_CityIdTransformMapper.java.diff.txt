diff --git a/product-detail-common-module-domain/src/main/java/com/sankuai/dzshoppingguide/product/detail/domain/idmapper/CityIdTransformMapper.java b/product-detail-common-module-domain/src/main/java/com/sankuai/dzshoppingguide/product/detail/domain/idmapper/CityIdTransformMapper.java
index c234dc77..90b3b52c 100644
--- a/product-detail-common-module-domain/src/main/java/com/sankuai/dzshoppingguide/product/detail/domain/idmapper/CityIdTransformMapper.java
+++ b/product-detail-common-module-domain/src/main/java/com/sankuai/dzshoppingguide/product/detail/domain/idmapper/CityIdTransformMapper.java
@@ -1,5 +1,6 @@
 package com.sankuai.dzshoppingguide.product.detail.domain.idmapper;
 
+import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
 import com.meituan.service.mobile.mtthrift.callback.OctoThriftCallback;
 import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
 import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
@@ -9,7 +10,6 @@ import com.sankuai.map.open.platform.api.transformcitytype.MtFrontInfo;
 import com.sankuai.map.open.platform.api.transformcitytype.TransformCityTypeRequest;
 import com.sankuai.map.open.platform.api.transformcitytype.TransformCityTypeResponse;
 import lombok.extern.slf4j.Slf4j;
-import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.stereotype.Service;
 
 import java.util.Optional;
@@ -22,7 +22,11 @@ import java.util.concurrent.CompletableFuture;
 @Service
 @Slf4j
 public class CityIdTransformMapper {
-    @Autowired
+    @MdpThriftClient(
+            timeout = 500, testTimeout = 5000,
+            remoteAppKey = "com.sankuai.apigw.map.facadecenter",
+            async = true
+    )
     private MapOpenApiService.AsyncIface mapOpenApiService;
 
     public CompletableFuture<Integer> transformCityId(int cityId, boolean mt) {
