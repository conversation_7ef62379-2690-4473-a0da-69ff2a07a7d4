diff --git a/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/fetcher/common/category/ProductCategoryName.java b/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/fetcher/common/category/ProductCategoryName.java
new file mode 100644
index 00000000..6f4801bd
--- /dev/null
+++ b/product-detail-common-module-application/src/main/java/com/sankuai/dzshoppingguide/product/detail/application/fetcher/common/category/ProductCategoryName.java
@@ -0,0 +1,16 @@
+package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category;
+
+import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
+import lombok.Builder;
+import lombok.Data;
+
+@Data
+@Builder
+public class ProductCategoryName extends FetcherReturnValueDTO{
+    private String secondCategoryName;
+
+    private String thirdCategoryName;
+
+    private String fourthCategoryName;
+    
+}
