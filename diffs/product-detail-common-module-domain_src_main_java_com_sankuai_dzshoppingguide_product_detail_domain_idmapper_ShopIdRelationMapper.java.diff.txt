diff --git a/product-detail-common-module-domain/src/main/java/com/sankuai/dzshoppingguide/product/detail/domain/idmapper/ShopIdRelationMapper.java b/product-detail-common-module-domain/src/main/java/com/sankuai/dzshoppingguide/product/detail/domain/idmapper/ShopIdRelationMapper.java
index cfe65109..3d6d09d4 100644
--- a/product-detail-common-module-domain/src/main/java/com/sankuai/dzshoppingguide/product/detail/domain/idmapper/ShopIdRelationMapper.java
+++ b/product-detail-common-module-domain/src/main/java/com/sankuai/dzshoppingguide/product/detail/domain/idmapper/ShopIdRelationMapper.java
@@ -4,9 +4,9 @@ import com.dianping.poi.relation.service.api.PoiRelationService;
 import com.dianping.poi.relation.service.dto.AdvancedPoiPairDTO;
 import com.dianping.vc.sdk.codec.JsonCodec;
 import com.meituan.inf.xmdlog.XMDLogFormat;
+import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
 import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
 import lombok.extern.slf4j.Slf4j;
-import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.stereotype.Service;
 
 import java.util.Optional;
@@ -20,14 +20,9 @@ import java.util.concurrent.CompletableFuture;
 @Service
 public class ShopIdRelationMapper {
 
-    // todo 为什么RpcClient方式注入失败
-    // @RpcClient(url = "com.dianping.poi.relation.service.api.PoiRelationService")
-    // private PoiRelationService poiRelationService;
-
-    @Autowired
+    @MdpThriftClient(remoteAppKey = "poi-relation-service-web", timeout = 1000, testTimeout = 5000, async = true)
     private PoiRelationService poiRelationServiceFuture;
 
-
     public CompletableFuture<Long> getDpPoiIdByMtPoiId(long mtShopId) {
         try {
             poiRelationServiceFuture.queryPoiPairByMtIdL(mtShopId);
@@ -46,6 +41,7 @@ public class ShopIdRelationMapper {
                         return Optional.ofNullable(result.getDpId()).orElse(0L);
                     });
         } catch (Exception e) {
+            log.error("getDpPoiIdByMtPoiId error", e);
             return CompletableFuture.completedFuture(0L);
         }
     }
