# 团购详情页商品属性展示技术解决方案

## 1. 需求分析

### 1.1 功能需求分析

基于 `requirement1.md` 的需求分析，核心功能需求包括：

**主要功能需求：**
- 团购详情页增加"团单分类"和"商品属性"字段展示
- 商品属性按优先级排序：分类 > 品牌 > 规格 > 商品品类 > 保质期 > 生产日期 > 规格 > 口味 > 包装
- 最多展示5个属性，空值不展示
- 属性字段值为空时不下发该字段
- 支持闪购侧属性与普通属性的统一展示

**非功能需求：**
- 向后兼容现有团详模块
- 支持动态配置属性展示规则
- 高性能数据获取和处理
- 支持多业务场景扩展

### 1.2 约束条件

- 必须基于现有DDD架构和模块编排框架
- 复用现有的ProductAttrFetcher和ProductCategoryFetcher
- 遵循现有的Lion配置管理机制
- 保持与现有团详模块的一致性

## 2. 架构上下文分析

### 2.1 现有架构基础

基于 `index.md` 的架构分析，当前系统具备以下技术基础：

**已有核心组件：**
- `ModuleDetailStructuredDetailVO<T>` - 泛型化结构化团详模块
- `ProductAttrFetcher` - 商品属性获取器
- `ProductCategoryFetcher` - 商品分类获取器
- `ProductAttrConfigService` - 商品属性配置服务
- `CpvAttrConfigService` - 闪购侧属性配置服务

**现有数据流：**
```
请求 → ProductAttrFetcher → QueryCenterAggregateFetcher → 查询中心 → AttrDTO → ProductAttr
```

**配置管理机制：**
- Lion配置中心动态配置
- 基于ProductTypeEnum和二级分类ID的配置体系
- 支持属性优先级配置

### 2.2 设计原则遵循

- **DDD原则**：领域驱动设计，明确业务边界
- **单一职责**：每个组件职责明确
- **开闭原则**：对扩展开放，对修改封闭
- **最小侵入**：最小化对现有代码的修改

## 3. 技术解决方案设计

### 3.1 整体架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    团购详情页商品属性模块                      │
├─────────────────────────────────────────────────────────────┤
│  API层: GroupBuyingProductAttributeVO (新增)                │
├─────────────────────────────────────────────────────────────┤
│  应用层: GroupBuyingAttributeModuleBuilder (新增)            │
│         ProductAttributeDisplayService (新增)               │
├─────────────────────────────────────────────────────────────┤
│  领域层: ProductAttributePriorityService (新增)              │
│         ProductAttributeConfigService (扩展)                │
├─────────────────────────────────────────────────────────────┤
│  基础设施层: 复用现有Fetcher和配置服务                        │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 核心组件设计

#### 3.2.1 数据传输对象设计

**GroupBuyingProductAttributeVO** (新增)
```java
@Data
@TypeDoc(description = "团购商品属性展示模块")
@MobileDo(id = 0x[新ID])
public class GroupBuyingProductAttributeVO implements Serializable {
    
    @FieldDoc(description = "团单分类")
    @MobileDo.MobileField(key = 0x[新KEY])
    private String groupBuyingCategory;
    
    @FieldDoc(description = "商品属性列表")
    @MobileDo.MobileField(key = 0x[新KEY])
    private List<ProductAttributeItem> productAttributes;
}

@Data
@TypeDoc(description = "商品属性项")
@MobileDo(id = 0x[新ID])
public class ProductAttributeItem implements Serializable {
    
    @FieldDoc(description = "属性名称")
    @MobileDo.MobileField(key = 0x[新KEY])
    private String attributeName;
    
    @FieldDoc(description = "属性值")
    @MobileDo.MobileField(key = 0x[新KEY])
    private String attributeValue;
    
    @FieldDoc(description = "优先级")
    @MobileDo.MobileField(key = 0x[新KEY])
    private Integer priority;
}
```

#### 3.2.2 模块构建器设计

**GroupBuyingAttributeModuleBuilder** (新增)
```java
@Builder(
    moduleKey = ModuleKeyConstants.GROUP_BUYING_PRODUCT_ATTRIBUTE,
    startFetcher = CommonModuleStarter.class,
    dependentFetchers = {
        ProductAttrFetcher.class,
        ProductCategoryFetcher.class
    }
)
public class GroupBuyingAttributeModuleBuilder extends BaseBuilder<GroupBuyingProductAttributeVO> {
    
    @Resource
    private ProductAttributeDisplayService productAttributeDisplayService;
    
    @Override
    public GroupBuyingProductAttributeVO doBuild() {
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        
        return productAttributeDisplayService.buildGroupBuyingAttributeModule(
            productAttr, productCategory, request.getProductTypeEnum()
        );
    }
}
```

#### 3.2.3 业务服务设计

**ProductAttributeDisplayService** (新增)
```java
@Service
public class ProductAttributeDisplayService {
    
    @Resource
    private ProductAttributePriorityService priorityService;
    
    @Resource
    private ProductAttrConfigService configService;
    
    public GroupBuyingProductAttributeVO buildGroupBuyingAttributeModule(
            ProductAttr productAttr, 
            ProductCategory productCategory, 
            ProductTypeEnum productType) {
        
        // 1. 构建团单分类
        String groupBuyingCategory = buildGroupBuyingCategory(productCategory);
        
        // 2. 获取并排序商品属性
        List<ProductAttributeItem> attributes = buildProductAttributes(
            productAttr, productCategory, productType
        );
        
        // 3. 应用展示规则（最多5个，过滤空值）
        List<ProductAttributeItem> displayAttributes = applyDisplayRules(attributes);
        
        return GroupBuyingProductAttributeVO.builder()
            .groupBuyingCategory(groupBuyingCategory)
            .productAttributes(displayAttributes)
            .build();
    }
    
    private List<ProductAttributeItem> buildProductAttributes(
            ProductAttr productAttr, 
            ProductCategory productCategory, 
            ProductTypeEnum productType) {
        
        Map<String, AttrDTO> attrMap = productAttr.getAttrMap();
        Map<String, Integer> priorityMap = priorityService.getAttributePriorityMap(
            productType, productCategory.getProductSecondCategoryId()
        );
        
        return attrMap.entrySet().stream()
            .filter(entry -> hasValidValue(entry.getValue()))
            .map(entry -> buildAttributeItem(entry, priorityMap))
            .sorted(Comparator.comparing(ProductAttributeItem::getPriority))
            .collect(Collectors.toList());
    }
    
    private List<ProductAttributeItem> applyDisplayRules(List<ProductAttributeItem> attributes) {
        return attributes.stream()
            .filter(attr -> StringUtils.isNotBlank(attr.getAttributeValue()))
            .limit(5)
            .collect(Collectors.toList());
    }
}
```

#### 3.2.4 领域服务设计

**ProductAttributePriorityService** (新增)
```java
@Service
public class ProductAttributePriorityService implements InitializingBean {
    
    private final String ATTRIBUTE_PRIORITY_CONFIG = 
        "com.sankuai.dzshoppingguide.detail.commonmodule.attribute.priority.config";
    
    private Map<String, Map<String, Integer>> priorityConfigMap = new ConcurrentHashMap<>();
    
    // 默认优先级配置
    private static final Map<String, Integer> DEFAULT_PRIORITY_MAP = Map.of(
        "分类", 1,
        "品牌", 2,
        "规格", 3,
        "商品品类", 4,
        "保质期", 5,
        "生产日期", 6,
        "口味", 7,
        "包装", 8
    );
    
    public Map<String, Integer> getAttributePriorityMap(
            ProductTypeEnum productType, 
            int secondCategoryId) {
        
        String configKey = buildConfigKey(productType, secondCategoryId);
        Map<String, Integer> configPriority = priorityConfigMap.get(configKey);
        
        if (configPriority != null) {
            return configPriority;
        }
        
        return DEFAULT_PRIORITY_MAP;
    }
    
    @Override
    public void afterPropertiesSet() throws Exception {
        parse(Lion.getString(Environment.getAppName(), ATTRIBUTE_PRIORITY_CONFIG));
        Lion.addConfigListener(ATTRIBUTE_PRIORITY_CONFIG, 
            configEvent -> parse(configEvent.getValue()));
    }
}
```

### 3.3 配置管理设计

#### 3.3.1 Lion配置扩展

**新增配置项：**
```json
{
  "com.sankuai.dzshoppingguide.detail.commonmodule.attribute.priority.config": {
    "DEAL-303": {
      "分类": 1,
      "品牌": 2,
      "规格": 3,
      "商品品类": 4,
      "保质期": 5
    }
  },
  "com.sankuai.dzshoppingguide.detail.commonmodule.attribute.display.config": {
    "maxDisplayCount": 5,
    "filterEmptyValues": true,
    "enableCategoryDisplay": true
  }
}
```

#### 3.3.2 ProductAttrConfigService扩展

扩展现有服务以支持新的属性配置需求：
```java
// 在现有ProductAttrConfigService中新增方法
public Set<String> getGroupBuyingDisplayAttrs(ProductTypeEnum productType, int secondProductCategory) {
    // 获取团购展示相关的属性配置
    Set<String> baseAttrs = getProductAttrConfig(productType, secondProductCategory);
    Set<String> displayAttrs = getDisplayAttrConfig(productType, secondProductCategory);
    
    Set<String> result = new HashSet<>(baseAttrs);
    result.addAll(displayAttrs);
    return Collections.unmodifiableSet(result);
}
```

## 4. 实施计划

### 4.1 第一阶段：基础组件开发（1-2周）

**目标：** 建立核心数据结构和服务框架

**具体任务：**
1. 创建API层数据传输对象
   - `GroupBuyingProductAttributeVO`
   - `ProductAttributeItem`
   - 更新`ModuleKeyConstants`

2. 实现领域服务
   - `ProductAttributePriorityService`
   - 扩展`ProductAttrConfigService`

3. 配置Lion配置项
   - 属性优先级配置
   - 展示规则配置

**风险缓解：**
- 使用feature flag控制新功能开关
- 保持向后兼容性
- 充分的单元测试覆盖

### 4.2 第二阶段：业务逻辑实现（2-3周）

**目标：** 实现核心业务逻辑和模块构建器

**具体任务：**
1. 实现应用服务
   - `ProductAttributeDisplayService`
   - `GroupBuyingAttributeModuleBuilder`

2. 集成现有Fetcher
   - 扩展`ProductAttrFetcher`使用新配置
   - 确保与`ProductCategoryFetcher`的协同

3. 实现属性处理逻辑
   - 属性排序算法
   - 空值过滤逻辑
   - 展示数量限制

**风险缓解：**
- 渐进式集成测试
- 性能基准测试
- 异常处理机制完善

### 4.3 第三阶段：集成测试与优化（1-2周）

**目标：** 完成系统集成和性能优化

**具体任务：**
1. 端到端集成测试
   - 与现有团详模块的集成
   - 多业务场景验证
   - 边界条件测试

2. 性能优化
   - 缓存策略优化
   - 数据获取性能调优
   - 内存使用优化

3. 监控和日志
   - 添加关键指标监控
   - 完善错误日志
   - 性能指标收集

**风险缓解：**
- 灰度发布策略
- 回滚方案准备
- 监控告警配置

### 4.4 第四阶段：上线部署（1周）

**目标：** 安全稳定上线

**具体任务：**
1. 预发布环境验证
2. 生产环境灰度发布
3. 全量发布
4. 上线后监控

**风险缓解：**
- 分批次发布
- 实时监控关键指标
- 快速回滚机制

## 5. 技术规范

### 5.1 代码规范

- 遵循现有项目的代码风格和命名规范
- 使用Lombok减少样板代码
- 充分的JavaDoc注释
- 单元测试覆盖率不低于80%

### 5.2 性能要求

- 属性处理延迟不超过10ms
- 内存使用增长不超过5%
- 支持并发访问无性能退化

### 5.3 可维护性要求

- 模块化设计，职责清晰
- 配置化管理，支持动态调整
- 完善的错误处理和日志记录
- 向后兼容性保证

## 6. 风险评估与缓解

### 6.1 技术风险

**风险：** 与现有系统集成复杂度高
**缓解措施：** 
- 充分的架构分析和设计评审
- 渐进式开发和集成
- 完善的测试策略

**风险：** 性能影响
**缓解措施：**
- 性能基准测试
- 缓存策略优化
- 异步处理机制

### 6.2 业务风险

**风险：** 需求理解偏差
**缓解措施：**
- 详细的需求澄清和确认
- 原型验证
- 迭代式开发

**风险：** 上线影响现有功能
**缓解措施：**
- Feature flag控制
- 灰度发布
- 快速回滚机制

## 7. 成功标准

### 7.1 功能标准
- 团购详情页正确展示团单分类和商品属性
- 属性按优先级正确排序
- 空值过滤和数量限制正确执行
- 配置动态生效

### 7.2 性能标准
- 响应时间不超过现有基线的10%
- 内存使用增长控制在5%以内
- 并发处理能力不下降

### 7.3 质量标准
- 单元测试覆盖率≥80%
- 集成测试通过率100%
- 代码审查通过
- 文档完整性检查通过

## 8. 后续扩展规划

### 8.1 功能扩展
- 支持更多属性类型展示
- 个性化属性展示规则
- 属性展示样式定制

### 8.2 技术优化
- 属性数据缓存优化
- 配置管理平台化
- 监控和告警完善

## 9. 架构图设计

### 9.1 系统架构图

```mermaid
graph TB
    subgraph "API层"
        A[GroupBuyingProductAttributeVO]
        B[ProductAttributeItem]
    end

    subgraph "应用层"
        C[GroupBuyingAttributeModuleBuilder]
        D[ProductAttributeDisplayService]
    end

    subgraph "领域层"
        E[ProductAttributePriorityService]
        F[ProductAttrConfigService扩展]
        G[CpvAttrConfigService]
    end

    subgraph "基础设施层"
        H[ProductAttrFetcher]
        I[ProductCategoryFetcher]
        J[QueryCenterAggregateFetcher]
        K[Lion配置中心]
    end

    C --> D
    C --> H
    C --> I
    D --> E
    D --> F
    E --> K
    F --> G
    F --> K
    H --> J
    I --> J
```

### 9.2 数据流图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Builder as GroupBuyingAttributeModuleBuilder
    participant Service as ProductAttributeDisplayService
    participant AttrFetcher as ProductAttrFetcher
    participant CategoryFetcher as ProductCategoryFetcher
    participant PriorityService as ProductAttributePriorityService
    participant ConfigService as ProductAttrConfigService
    participant Lion as Lion配置中心

    Client->>Builder: 请求团购属性模块
    Builder->>AttrFetcher: 获取商品属性
    Builder->>CategoryFetcher: 获取商品分类
    AttrFetcher-->>Builder: 返回ProductAttr
    CategoryFetcher-->>Builder: 返回ProductCategory
    Builder->>Service: 构建属性展示模块
    Service->>PriorityService: 获取属性优先级
    PriorityService->>Lion: 读取优先级配置
    Lion-->>PriorityService: 返回配置
    PriorityService-->>Service: 返回优先级映射
    Service->>ConfigService: 获取属性配置
    ConfigService-->>Service: 返回属性配置
    Service-->>Builder: 返回构建结果
    Builder-->>Client: 返回团购属性模块
```

## 10. 详细实现规范

### 10.1 模块键常量扩展

```java
// 在ModuleKeyConstants中新增
public static final String GROUP_BUYING_PRODUCT_ATTRIBUTE = "group_buying_product_attribute";
```

### 10.2 错误处理策略

```java
@Component
public class ProductAttributeErrorHandler {

    private static final Logger log = LoggerFactory.getLogger(ProductAttributeErrorHandler.class);

    public GroupBuyingProductAttributeVO handleAttributeError(Exception e, ProductTypeEnum productType) {
        log.error("构建团购商品属性模块失败, productType: {}", productType, e);

        // 返回默认的空模块，保证页面正常展示
        return GroupBuyingProductAttributeVO.builder()
            .groupBuyingCategory("")
            .productAttributes(Collections.emptyList())
            .build();
    }
}
```

### 10.3 缓存策略设计

```java
@Service
public class ProductAttributeCacheService {

    @Resource
    private RedisClientUtils redisClient;

    private static final String CACHE_KEY_PREFIX = "product_attr_priority:";
    private static final int CACHE_EXPIRE_TIME = 3600; // 1小时

    public Map<String, Integer> getCachedPriorityMap(String configKey) {
        String cacheKey = CACHE_KEY_PREFIX + configKey;
        return redisClient.get(cacheKey, new TypeReference<Map<String, Integer>>() {});
    }

    public void cachePriorityMap(String configKey, Map<String, Integer> priorityMap) {
        String cacheKey = CACHE_KEY_PREFIX + configKey;
        redisClient.setex(cacheKey, CACHE_EXPIRE_TIME, priorityMap);
    }
}
```

### 10.4 监控指标定义

```java
@Component
public class ProductAttributeMetrics {

    private static final String METRIC_PREFIX = "product_attribute_";

    public void recordAttributeProcessTime(long processTime) {
        // 记录属性处理耗时
        XmdLogger.recordTime(METRIC_PREFIX + "process_time", processTime);
    }

    public void recordAttributeCount(int count) {
        // 记录属性数量
        XmdLogger.recordCount(METRIC_PREFIX + "count", count);
    }

    public void recordConfigMiss(String configKey) {
        // 记录配置缺失
        XmdLogger.recordCount(METRIC_PREFIX + "config_miss", 1, "config_key", configKey);
    }
}
```

## 11. 测试策略

### 11.1 单元测试

```java
@ExtendWith(MockitoExtension.class)
class ProductAttributeDisplayServiceTest {

    @Mock
    private ProductAttributePriorityService priorityService;

    @Mock
    private ProductAttrConfigService configService;

    @InjectMocks
    private ProductAttributeDisplayService displayService;

    @Test
    void testBuildGroupBuyingAttributeModule_Success() {
        // Given
        ProductAttr productAttr = mockProductAttr();
        ProductCategory productCategory = mockProductCategory();
        ProductTypeEnum productType = ProductTypeEnum.DEAL;

        when(priorityService.getAttributePriorityMap(productType, 303))
            .thenReturn(mockPriorityMap());

        // When
        GroupBuyingProductAttributeVO result = displayService
            .buildGroupBuyingAttributeModule(productAttr, productCategory, productType);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getProductAttributes()).hasSize(5);
        assertThat(result.getProductAttributes().get(0).getAttributeName()).isEqualTo("分类");
    }

    @Test
    void testApplyDisplayRules_FilterEmptyValues() {
        // 测试空值过滤逻辑
    }

    @Test
    void testApplyDisplayRules_LimitToFive() {
        // 测试数量限制逻辑
    }
}
```

### 11.2 集成测试

```java
@SpringBootTest
@TestPropertySource(properties = {
    "com.sankuai.dzshoppingguide.detail.commonmodule.attribute.priority.config=" +
    "{\"DEAL-303\":{\"分类\":1,\"品牌\":2,\"规格\":3}}"
})
class GroupBuyingAttributeModuleBuilderIntegrationTest {

    @Autowired
    private GroupBuyingAttributeModuleBuilder builder;

    @Test
    void testBuildModule_WithRealData() {
        // 使用真实数据进行集成测试
    }
}
```

### 11.3 性能测试

```java
@Component
public class ProductAttributePerformanceTest {

    @Test
    void testAttributeProcessingPerformance() {
        // 性能基准测试
        long startTime = System.currentTimeMillis();

        // 执行属性处理逻辑

        long endTime = System.currentTimeMillis();
        long processTime = endTime - startTime;

        // 断言处理时间在可接受范围内
        assertThat(processTime).isLessThan(10); // 10ms以内
    }
}
```

## 12. 部署和运维

### 12.1 部署配置

```yaml
# application.yml 新增配置
product-attribute:
  display:
    max-count: 5
    enable-cache: true
    cache-expire-time: 3600
  priority:
    default-config: "分类:1,品牌:2,规格:3,商品品类:4,保质期:5"
```

### 12.2 监控告警

```yaml
# 监控配置
alerts:
  - name: product_attribute_error_rate
    condition: error_rate > 0.01
    action: send_alert

  - name: product_attribute_response_time
    condition: avg_response_time > 50ms
    action: send_alert
```

### 12.3 日志规范

```java
// 统一日志格式
log.info("构建团购商品属性模块开始, dealGroupId: {}, productType: {}, secondCategoryId: {}",
    dealGroupId, productType, secondCategoryId);

log.info("属性处理完成, 原始属性数量: {}, 展示属性数量: {}, 处理耗时: {}ms",
    originalCount, displayCount, processTime);
```

这个技术解决方案遵循了现有的DDD架构模式，最小化了对现有代码的侵入性，同时提供了完整的实施路径和风险控制措施。通过模块化设计、配置化管理和完善的测试策略，确保了方案的可行性和可维护性。
